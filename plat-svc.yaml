apiVersion: v1
items:
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"app-blueprint","namespace":"lx-csrda"},"spec":{"externalTrafficPolicy":"Cluster","ports":[{"name":"svc","nodePort":30815,"port":8130,"protocol":"TCP","targetPort":8443}],"selector":{"app":"app-blueprint"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2019-12-26T04:40:18Z"
    name: app-blueprint
    namespace: lx-csrda
    resourceVersion: "116768"
    selfLink: /api/v1/namespaces/lx-csrda/services/app-blueprint
    uid: 3f451907-c7ed-44e5-92bc-06f2e971c84e
  spec:
    clusterIP: **************
    externalTrafficPolicy: Cluster
    ports:
    - name: svc
      nodePort: 30815
      port: 8130
      protocol: TCP
      targetPort: 8443
    selector:
      app: app-blueprint
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"app-conference-admin","namespace":"lx-csrda"},"spec":{"externalTrafficPolicy":"Cluster","ports":[{"name":"svc","nodePort":31178,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"app-conference-admin"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2019-12-26T04:40:18Z"
    name: app-conference-admin
    namespace: lx-csrda
    resourceVersion: "116772"
    selfLink: /api/v1/namespaces/lx-csrda/services/app-conference-admin
    uid: 302405e5-a98f-4172-bbca-f9d9e8de876c
  spec:
    clusterIP: ************
    externalTrafficPolicy: Cluster
    ports:
    - name: svc
      nodePort: 31178
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: app-conference-admin
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"app-conference-article","namespace":"lx-csrda"},"spec":{"externalTrafficPolicy":"Cluster","ports":[{"name":"svc","nodePort":31934,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"app-conference-article"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2019-12-26T04:40:18Z"
    name: app-conference-article
    namespace: lx-csrda
    resourceVersion: "116776"
    selfLink: /api/v1/namespaces/lx-csrda/services/app-conference-article
    uid: 1959744d-ff2e-41d4-9fcf-ef285fd7c663
  spec:
    clusterIP: ************
    externalTrafficPolicy: Cluster
    ports:
    - name: svc
      nodePort: 31934
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: app-conference-article
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"app-conference-mobile","namespace":"lx-csrda"},"spec":{"externalTrafficPolicy":"Cluster","ports":[{"name":"svc","nodePort":31709,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"app-conference-mobile"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2019-12-26T04:40:18Z"
    name: app-conference-mobile
    namespace: lx-csrda
    resourceVersion: "116780"
    selfLink: /api/v1/namespaces/lx-csrda/services/app-conference-mobile
    uid: bd8f05f0-461d-4594-9722-85fe3f19d470
  spec:
    clusterIP: *************
    externalTrafficPolicy: Cluster
    ports:
    - name: svc
      nodePort: 31709
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: app-conference-mobile
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"app-notify","namespace":"lx-csrda"},"spec":{"externalTrafficPolicy":"Cluster","ports":[{"name":"svc","nodePort":30654,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"app-notify"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2021-01-25T09:14:50Z"
    name: app-notify
    namespace: lx-csrda
    resourceVersion: "77966886"
    selfLink: /api/v1/namespaces/lx-csrda/services/app-notify
    uid: 6b8826ff-89bf-4aa2-82ed-2ffcc636fea0
  spec:
    clusterIP: *************
    externalTrafficPolicy: Cluster
    ports:
    - name: svc
      nodePort: 30654
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: app-notify
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"app-position-report","namespace":"lx-csrda"},"spec":{"externalTrafficPolicy":"Cluster","ports":[{"name":"svc","nodePort":30698,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"app-position-report"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2019-12-26T04:40:18Z"
    name: app-position-report
    namespace: lx-csrda
    resourceVersion: "116808"
    selfLink: /api/v1/namespaces/lx-csrda/services/app-position-report
    uid: 1e665037-ec63-400c-aa43-882d295c9457
  spec:
    clusterIP: *************
    externalTrafficPolicy: Cluster
    ports:
    - name: svc
      nodePort: 30698
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: app-position-report
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"app-survey","namespace":"lx-csrda"},"spec":{"externalTrafficPolicy":"Cluster","ports":[{"name":"svc","nodePort":31439,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"app-survey"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2019-12-26T04:40:59Z"
    name: app-survey
    namespace: lx-csrda
    resourceVersion: "11865063"
    selfLink: /api/v1/namespaces/lx-csrda/services/app-survey
    uid: a887a3f3-2c41-414c-be2c-a176c4a4b0ef
  spec:
    clusterIP: **************
    externalTrafficPolicy: Cluster
    ports:
    - name: svc
      nodePort: 31439
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: app-survey
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"app-survey-mobile","namespace":"lx-csrda"},"spec":{"externalTrafficPolicy":"Cluster","ports":[{"name":"svc","nodePort":30633,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"app-survey-mobile"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2019-12-26T04:40:18Z"
    name: app-survey-mobile
    namespace: lx-csrda
    resourceVersion: "116818"
    selfLink: /api/v1/namespaces/lx-csrda/services/app-survey-mobile
    uid: f5bbf774-ee73-4bd1-b588-931ee9f2ecdd
  spec:
    clusterIP: *************
    externalTrafficPolicy: Cluster
    ports:
    - name: svc
      nodePort: 30633
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: app-survey-mobile
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"app-teleconference-middleground","namespace":"lx-csrda"},"spec":{"externalTrafficPolicy":"Cluster","ports":[{"name":"web","nodePort":32184,"port":8010,"protocol":"TCP","targetPort":8010},{"name":"tls","nodePort":32578,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"app-teleconference-middleground"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2020-04-29T15:17:15Z"
    name: app-teleconference-middleground
    namespace: lx-csrda
    resourceVersion: "23864063"
    selfLink: /api/v1/namespaces/lx-csrda/services/app-teleconference-middleground
    uid: deb26fc3-58d9-4841-8c51-c03d082bcc5c
  spec:
    clusterIP: **************
    externalTrafficPolicy: Cluster
    ports:
    - name: web
      nodePort: 32184
      port: 8010
      protocol: TCP
      targetPort: 8010
    - name: tls
      nodePort: 32578
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: app-teleconference-middleground
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"app-teleconference-mobile","namespace":"lx-csrda"},"spec":{"externalTrafficPolicy":"Cluster","ports":[{"name":"web","nodePort":32425,"port":8010,"protocol":"TCP","targetPort":8010},{"name":"tls","nodePort":32544,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"app-teleconference-mobile"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2020-04-29T15:15:18Z"
    name: app-teleconference-mobile
    namespace: lx-csrda
    resourceVersion: "23863789"
    selfLink: /api/v1/namespaces/lx-csrda/services/app-teleconference-mobile
    uid: f719722a-9707-4392-a940-747e568cc249
  spec:
    clusterIP: *************
    externalTrafficPolicy: Cluster
    ports:
    - name: web
      nodePort: 32425
      port: 8010
      protocol: TCP
      targetPort: 8010
    - name: tls
      nodePort: 32544
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: app-teleconference-mobile
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"app-teleconference-pc","namespace":"lx-csrda"},"spec":{"externalTrafficPolicy":"Cluster","ports":[{"name":"web","nodePort":31542,"port":8010,"protocol":"TCP","targetPort":8010},{"name":"tls","nodePort":30464,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"app-teleconference-pc"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2020-04-29T15:03:58Z"
    name: app-teleconference-pc
    namespace: lx-csrda
    resourceVersion: "23862032"
    selfLink: /api/v1/namespaces/lx-csrda/services/app-teleconference-pc
    uid: 784c7ddd-24df-4fed-ba06-0114cdd9fd66
  spec:
    clusterIP: **************
    externalTrafficPolicy: Cluster
    ports:
    - name: web
      nodePort: 31542
      port: 8010
      protocol: TCP
      targetPort: 8010
    - name: tls
      nodePort: 30464
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: app-teleconference-pc
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"app-telephone-conference","namespace":"lx-csrda"},"spec":{"ports":[{"name":"web","nodePort":30172,"port":8010,"protocol":"TCP","targetPort":8010},{"name":"tls","nodePort":31476,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"app-telephone-conference"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2020-07-31T23:36:04Z"
    name: app-telephone-conference
    namespace: lx-csrda
    resourceVersion: "42059430"
    selfLink: /api/v1/namespaces/lx-csrda/services/app-telephone-conference
    uid: 5ed1266d-6393-4cab-a523-565d9d66b37a
  spec:
    clusterIP: **************
    externalTrafficPolicy: Cluster
    ports:
    - name: web
      nodePort: 30172
      port: 8010
      protocol: TCP
      targetPort: 8010
    - name: tls
      nodePort: 31476
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: app-telephone-conference
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"app-telephone-conference2","namespace":"lx-csrda"},"spec":{"ports":[{"name":"tls","nodePort":30606,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"app-telephone-conference2"},"type":"NodePort"}}
    creationTimestamp: "2022-04-18T07:24:44Z"
    name: app-telephone-conference2
    namespace: lx-csrda
    resourceVersion: "155584751"
    selfLink: /api/v1/namespaces/lx-csrda/services/app-telephone-conference2
    uid: 4fadcd01-ca2f-4437-88f1-f460dad42259
  spec:
    clusterIP: **************
    externalTrafficPolicy: Cluster
    ports:
    - name: tls
      nodePort: 30606
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: app-telephone-conference2
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"app3rd-notify-rd","namespace":"lx-csrda"},"spec":{"ports":[{"name":"svc","port":9116,"protocol":"TCP","targetPort":9115}],"selector":{"app":"3rd-party-app-notify-rd"},"sessionAffinity":"None","type":"ClusterIP"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2019-12-26T04:40:19Z"
    name: app3rd-notify-rd
    namespace: lx-csrda
    resourceVersion: "116829"
    selfLink: /api/v1/namespaces/lx-csrda/services/app3rd-notify-rd
    uid: e63f4502-778f-40e5-a640-838c539acc6f
  spec:
    clusterIP: ************
    ports:
    - name: svc
      port: 9116
      protocol: TCP
      targetPort: 9115
    selector:
      app: 3rd-party-app-notify-rd
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"app3rd-teleconference","namespace":"lx-csrda"},"spec":{"externalTrafficPolicy":"Cluster","ports":[{"name":"web","nodePort":30662,"port":8010,"protocol":"TCP","targetPort":7130},{"name":"tls","nodePort":30234,"port":8443,"protocol":"TCP","targetPort":7130}],"selector":{"app":"3rd-party-app-teleconference-rd"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2020-04-29T15:02:09Z"
    name: app3rd-teleconference
    namespace: lx-csrda
    resourceVersion: "23861784"
    selfLink: /api/v1/namespaces/lx-csrda/services/app3rd-teleconference
    uid: a7bb51e9-d6d2-4933-8ecb-46252ec2e6b1
  spec:
    clusterIP: ************
    externalTrafficPolicy: Cluster
    ports:
    - name: web
      nodePort: 30662
      port: 8010
      protocol: TCP
      targetPort: 7130
    - name: tls
      nodePort: 30234
      port: 8443
      protocol: TCP
      targetPort: 7130
    selector:
      app: 3rd-party-app-teleconference-rd
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"app3rd-teleconference2","namespace":"lx-csrda"},"spec":{"ports":[{"name":"svc","port":7130,"protocol":"TCP","targetPort":7130}],"selector":{"app":"3rd-party-app-teleconference-rd2"},"type":"ClusterIP"}}
    creationTimestamp: "2022-04-18T07:28:33Z"
    name: app3rd-teleconference2
    namespace: lx-csrda
    resourceVersion: "155585209"
    selfLink: /api/v1/namespaces/lx-csrda/services/app3rd-teleconference2
    uid: 90542685-c761-4db9-8338-edadf745e356
  spec:
    clusterIP: **************
    ports:
    - name: svc
      port: 7130
      protocol: TCP
      targetPort: 7130
    selector:
      app: 3rd-party-app-teleconference-rd2
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"blueprint-cmc","namespace":"lx-csrda"},"spec":{"ports":[{"name":"svc","port":8130,"protocol":"TCP","targetPort":8130}],"selector":{"app":"blueprint-cmc-adaption"},"sessionAffinity":"None","type":"ClusterIP"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2019-12-26T04:40:19Z"
    name: blueprint-cmc
    namespace: lx-csrda
    resourceVersion: "116842"
    selfLink: /api/v1/namespaces/lx-csrda/services/blueprint-cmc
    uid: 4aaab931-36cf-45e5-8d2e-b936f886f883
  spec:
    clusterIP: *************
    ports:
    - name: svc
      port: 8130
      protocol: TCP
      targetPort: 8130
    selector:
      app: blueprint-cmc-adaption
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"edge","namespace":"lx-csrda"},"spec":{"externalTrafficPolicy":"Cluster","ports":[{"name":"edge-5300","nodePort":31530,"port":5300,"protocol":"TCP","targetPort":5300},{"name":"unique","nodePort":31510,"port":5010,"protocol":"TCP","targetPort":5010}],"selector":{"app":"edge"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2019-12-26T04:39:49Z"
    name: edge
    namespace: lx-csrda
    resourceVersion: "116557"
    selfLink: /api/v1/namespaces/lx-csrda/services/edge
    uid: 813ce694-67b1-40ca-ab8b-78870cd04f9a
  spec:
    clusterIP: *************
    externalTrafficPolicy: Cluster
    ports:
    - name: edge-5300
      nodePort: 31530
      port: 5300
      protocol: TCP
      targetPort: 5300
    - name: unique
      nodePort: 31510
      port: 5010
      protocol: TCP
      targetPort: 5010
    selector:
      app: edge
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"imana-lvs","namespace":"lx-csrda"},"spec":{"externalTrafficPolicy":"Cluster","ports":[{"name":"nonssl","nodePort":31111,"port":31111,"protocol":"TCP","targetPort":5540},{"name":"ssl","nodePort":31112,"port":31112,"protocol":"TCP","targetPort":443}],"selector":{"app":"imana"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2019-12-26T04:39:50Z"
    name: imana-lvs
    namespace: lx-csrda
    resourceVersion: "154066974"
    selfLink: /api/v1/namespaces/lx-csrda/services/imana-lvs
    uid: 1e563524-42b5-4179-adf3-a9d25a788c0a
  spec:
    clusterIP: *************
    externalTrafficPolicy: Cluster
    ports:
    - name: nonssl
      nodePort: 31111
      port: 31111
      protocol: TCP
      targetPort: 8443
    - name: ssl
      nodePort: 31112
      port: 31112
      protocol: TCP
      targetPort: 8443
    selector:
      app: imana
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"lanxin-https-processing","namespace":"lx-csrda"},"spec":{"externalTrafficPolicy":"Cluster","ports":[{"name":"svc","nodePort":31002,"port":8088,"protocol":"TCP","targetPort":8088}],"selector":{"app":"lanxin-https-processing"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2021-04-13T09:12:54Z"
    name: lanxin-https-processing
    namespace: lx-csrda
    resourceVersion: "91528653"
    selfLink: /api/v1/namespaces/lx-csrda/services/lanxin-https-processing
    uid: 5d533df1-fd87-48f4-981a-8e449092a8ea
  spec:
    clusterIP: ***********
    externalTrafficPolicy: Cluster
    ports:
    - name: svc
      nodePort: 31002
      port: 8088
      protocol: TCP
      targetPort: 8088
    selector:
      app: lanxin-https-processing
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"lanxin-open-api-gateway","namespace":"lx-csrda"},"spec":{"ports":[{"name":"ssl","nodePort":31102,"port":30560,"protocol":"TCP","targetPort":5443}],"selector":{"app":"lanxin-open-api-gateway"},"type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2020-10-23T09:46:39Z"
    name: lanxin-open-api-gateway
    namespace: lx-csrda
    resourceVersion: "154066860"
    selfLink: /api/v1/namespaces/lx-csrda/services/lanxin-open-api-gateway
    uid: 57b40b45-f916-4680-9f58-12efb9a52b3f
  spec:
    clusterIP: *************
    externalTrafficPolicy: Cluster
    ports:
    - name: ssl
      nodePort: 31102
      port: 30560
      protocol: TCP
      targetPort: 8443
    selector:
      app: lanxin-open-api-gateway
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"lanxin-open-api-gateway-lvs","namespace":"lx-csrda"},"spec":{"externalTrafficPolicy":"Cluster","ports":[{"name":"nonssl","nodePort":30591,"port":30591,"protocol":"TCP","targetPort":5443},{"name":"ssl","nodePort":30560,"port":30560,"protocol":"TCP","targetPort":5443}],"selector":{"app":"lanxin-open-api-gateway"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2019-12-26T04:39:50Z"
    name: lanxin-open-api-gateway-lvs
    namespace: lx-csrda
    resourceVersion: "116573"
    selfLink: /api/v1/namespaces/lx-csrda/services/lanxin-open-api-gateway-lvs
    uid: 44f16733-765a-496e-855f-1e4074ca97a2
  spec:
    clusterIP: *************
    externalTrafficPolicy: Cluster
    ports:
    - name: nonssl
      nodePort: 30591
      port: 30591
      protocol: TCP
      targetPort: 5443
    - name: ssl
      nodePort: 30560
      port: 30560
      protocol: TCP
      targetPort: 5443
    selector:
      app: lanxin-open-api-gateway
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"lanxin-preview","namespace":"lx-csrda"},"spec":{"ports":[{"name":"svc","nodePort":32022,"port":3003,"protocol":"TCP","targetPort":3003}],"selector":{"app":"lanxin-preview"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2020-07-31T15:57:56Z"
    name: lanxin-preview
    namespace: lx-csrda
    resourceVersion: "41983660"
    selfLink: /api/v1/namespaces/lx-csrda/services/lanxin-preview
    uid: 8fbeb163-b7f5-41e2-9736-d811c31ef59b
  spec:
    clusterIP: *************
    externalTrafficPolicy: Cluster
    ports:
    - name: svc
      nodePort: 32022
      port: 3003
      protocol: TCP
      targetPort: 3003
    selector:
      app: lanxin-preview
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"lanxin-uc","namespace":"lx-csrda"},"spec":{"ports":[{"name":"svc","port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"lanxin-uc"},"type":"ClusterIP"}}
    creationTimestamp: "2022-04-09T11:18:18Z"
    name: lanxin-uc
    namespace: lx-csrda
    resourceVersion: "154041810"
    selfLink: /api/v1/namespaces/lx-csrda/services/lanxin-uc
    uid: 69f5b93d-a590-4141-aeb0-fdd2bb8d0d95
  spec:
    clusterIP: **************
    ports:
    - name: svc
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: lanxin-uc
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"lanxin-uc-passport","namespace":"lx-csrda"},"spec":{"ports":[{"name":"svc","port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"lanxin-uc"},"type":"ClusterIP"}}
    creationTimestamp: "2022-04-09T11:19:07Z"
    name: lanxin-uc-passport
    namespace: lx-csrda
    resourceVersion: "*********"
    selfLink: /api/v1/namespaces/lx-csrda/services/lanxin-uc-passport
    uid: b406f228-2c38-4075-a373-d8d631102ffc
  spec:
    clusterIP: *************
    ports:
    - name: svc
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: lanxin-uc-passport
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"lanxin-web","namespace":"lx-csrda"},"spec":{"externalTrafficPolicy":"Cluster","ports":[{"name":"svc","nodePort":30288,"port":31434,"protocol":"TCP","targetPort":443}],"selector":{"app":"lanxin-web"},"sessionAffinity":"None","type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2020-04-23T09:04:06Z"
    name: lanxin-web
    namespace: lx-csrda
    resourceVersion: "22610974"
    selfLink: /api/v1/namespaces/lx-csrda/services/lanxin-web
    uid: ff1d70cf-a9b1-431d-817a-7eca207248e5
  spec:
    clusterIP: *************
    externalTrafficPolicy: Cluster
    ports:
    - name: svc
      nodePort: 30288
      port: 31434
      protocol: TCP
      targetPort: 443
    selector:
      app: lanxin-web
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"lxweb-admin-developer","namespace":"lx-csrda"},"spec":{"ports":[{"name":"svc","nodePort":31100,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"lxweb-admin-developer"},"type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2020-10-23T09:46:38Z"
    name: lxweb-admin-developer
    namespace: lx-csrda
    resourceVersion: "59913581"
    selfLink: /api/v1/namespaces/lx-csrda/services/lxweb-admin-developer
    uid: df9e5ee8-67ab-4c9a-84ca-e9504252cb23
  spec:
    clusterIP: **************
    externalTrafficPolicy: Cluster
    ports:
    - name: svc
      nodePort: 31100
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: lxweb-admin-developer
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"lxweb-admin-emc","namespace":"lx-csrda"},"spec":{"ports":[{"name":"ssl","nodePort":31103,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"lxweb-admin-emc"},"type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2020-10-23T09:46:39Z"
    name: lxweb-admin-emc
    namespace: lx-csrda
    resourceVersion: "59913594"
    selfLink: /api/v1/namespaces/lx-csrda/services/lxweb-admin-emc
    uid: d30ab735-5f09-40f7-b536-f3b80fc4a113
  spec:
    clusterIP: *************
    externalTrafficPolicy: Cluster
    ports:
    - name: ssl
      nodePort: 31103
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: lxweb-admin-emc
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"lxweb-admin-mp","namespace":"lx-csrda"},"spec":{"ports":[{"name":"ssl","nodePort":31104,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"lxweb-admin-mp"},"type":"NodePort"},"status":{"loadBalancer":{}}}
    creationTimestamp: "2020-10-23T09:46:39Z"
    name: lxweb-admin-mp
    namespace: lx-csrda
    resourceVersion: "59913598"
    selfLink: /api/v1/namespaces/lx-csrda/services/lxweb-admin-mp
    uid: 6d6e5f13-a076-40cc-85c8-62156ae18671
  spec:
    clusterIP: **************
    externalTrafficPolicy: Cluster
    ports:
    - name: ssl
      nodePort: 31104
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: lxweb-admin-mp
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"lxweb-article","namespace":"lx-csrda"},"spec":{"ports":[{"name":"svc","nodePort":31106,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"lxweb-article"},"type":"NodePort"}}
    creationTimestamp: "2020-10-23T17:18:57Z"
    name: lxweb-article
    namespace: lx-csrda
    resourceVersion: "59980060"
    selfLink: /api/v1/namespaces/lx-csrda/services/lxweb-article
    uid: aa98cbca-dcc8-4ff1-9c73-bc7373eb321b
  spec:
    clusterIP: ************
    externalTrafficPolicy: Cluster
    ports:
    - name: svc
      nodePort: 31106
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: lxweb-article
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"lxweb-datahouse","namespace":"lx-csrda"},"spec":{"ports":[{"name":"ssl","nodePort":30009,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"lxweb-datahouse"},"type":"NodePort"}}
    creationTimestamp: "2022-04-09T08:23:05Z"
    name: lxweb-datahouse
    namespace: lx-csrda
    resourceVersion: "154020767"
    selfLink: /api/v1/namespaces/lx-csrda/services/lxweb-datahouse
    uid: 0a5cb041-7629-4b94-967a-6306249c43d7
  spec:
    clusterIP: *************
    externalTrafficPolicy: Cluster
    ports:
    - name: ssl
      nodePort: 30009
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: lxweb-datahouse
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"lxweb-uc","namespace":"lx-csrda"},"spec":{"ports":[{"name":"svc","nodePort":30001,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"lxweb-uc"},"type":"NodePort"}}
    creationTimestamp: "2022-04-09T11:21:40Z"
    name: lxweb-uc
    namespace: lx-csrda
    resourceVersion: "154042221"
    selfLink: /api/v1/namespaces/lx-csrda/services/lxweb-uc
    uid: ad136e75-d0e6-49f6-a5e7-cadcd9644b16
  spec:
    clusterIP: ************
    externalTrafficPolicy: Cluster
    ports:
    - name: svc
      nodePort: 30001
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: lxweb-uc
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"name":"lxweb-uc-passport","namespace":"lx-csrda"},"spec":{"ports":[{"name":"svc","nodePort":30002,"port":8443,"protocol":"TCP","targetPort":8443}],"selector":{"app":"lxweb-uc-passport"},"type":"NodePort"}}
    creationTimestamp: "2022-04-09T11:21:51Z"
    name: lxweb-uc-passport
    namespace: lx-csrda
    resourceVersion: "*********"
    selfLink: /api/v1/namespaces/lx-csrda/services/lxweb-uc-passport
    uid: 20b75f62-4046-4c80-81db-631fa7a41381
  spec:
    clusterIP: **************
    externalTrafficPolicy: Cluster
    ports:
    - name: svc
      nodePort: 30002
      port: 8443
      protocol: TCP
      targetPort: 8443
    selector:
      app: lxweb-uc-passport
    sessionAffinity: None
    type: NodePort
  status:
    loadBalancer: {}
kind: List
metadata:
  resourceVersion: ""
  selfLink: ""
